import hre from "hardhat";
import * as fs from "fs";
import * as path from "path";

async function main() {
  console.log("Deploying SimpleVoting contract...");

  // Deploy the contract
  const simpleVoting = await hre.viem.deployContract("SimpleVoting");
  
  console.log(`SimpleVoting deployed to: ${simpleVoting.address}`);

  // Save contract address and ABI to files for frontend use
  const contractAddresses = {
    SimpleVoting: simpleVoting.address,
    network: "base-sepolia",
    chainId: 84532,
    deployedAt: new Date().toISOString(),
  };

  // Save contract addresses
  fs.writeFileSync(
    "contract-addresses.json",
    JSON.stringify(contractAddresses, null, 2)
  );

  // Save ABI for frontend
  const artifactPath = path.join(__dirname, "../artifacts/contracts/SimpleVoting.sol/SimpleVoting.json");
  if (fs.existsSync(artifactPath)) {
    const artifact = JSON.parse(fs.readFileSync(artifactPath, "utf8"));
    
    // Create src/lib directory if it doesn't exist
    const libDir = path.join(__dirname, "../src/lib");
    if (!fs.existsSync(libDir)) {
      fs.mkdirSync(libDir, { recursive: true });
    }
    
    // Save ABI
    fs.writeFileSync(
      path.join(libDir, "contract-abi.json"),
      JSON.stringify(artifact.abi, null, 2)
    );
    
    // Save contract config
    const contractConfig = {
      address: simpleVoting.address,
      abi: artifact.abi,
      chainId: 84532,
    };
    
    fs.writeFileSync(
      path.join(libDir, "contract-config.json"),
      JSON.stringify(contractConfig, null, 2)
    );
    
    console.log("Contract ABI and config saved to src/lib/");
  }

  console.log("Deployment completed successfully!");
  console.log("Next steps:");
  console.log("1. Verify the contract on BaseScan");
  console.log("2. Update your .env file with the contract address");
  console.log("3. Test the contract functions on BaseScan");
}

main().catch((error) => {
  console.error("Deployment failed:", error);
  process.exitCode = 1;
});
