/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "./common";

export interface SimpleVotingInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "createPoll"
      | "creatorPolls"
      | "getCreatorPolls"
      | "getPoll"
      | "getPollCount"
      | "getUserVote"
      | "hasUserVoted"
      | "pollCount"
      | "polls"
      | "togglePollStatus"
      | "vote"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic: "PollCreated" | "PollStatusChanged" | "VoteCast"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "createPoll",
    values: [string, string[]]
  ): string;
  encodeFunctionData(
    functionFragment: "creatorPolls",
    values: [AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "getCreatorPolls",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "getPoll",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "getPollCount",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getUserVote",
    values: [BigNumberish, AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "hasUserVoted",
    values: [BigNumberish, AddressLike]
  ): string;
  encodeFunctionData(functionFragment: "pollCount", values?: undefined): string;
  encodeFunctionData(functionFragment: "polls", values: [BigNumberish]): string;
  encodeFunctionData(
    functionFragment: "togglePollStatus",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "vote",
    values: [BigNumberish, BigNumberish]
  ): string;

  decodeFunctionResult(functionFragment: "createPoll", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "creatorPolls",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getCreatorPolls",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "getPoll", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getPollCount",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getUserVote",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "hasUserVoted",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "pollCount", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "polls", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "togglePollStatus",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "vote", data: BytesLike): Result;
}

export namespace PollCreatedEvent {
  export type InputTuple = [
    pollId: BigNumberish,
    creator: AddressLike,
    question: string,
    optionCount: BigNumberish
  ];
  export type OutputTuple = [
    pollId: bigint,
    creator: string,
    question: string,
    optionCount: bigint
  ];
  export interface OutputObject {
    pollId: bigint;
    creator: string;
    question: string;
    optionCount: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace PollStatusChangedEvent {
  export type InputTuple = [pollId: BigNumberish, isActive: boolean];
  export type OutputTuple = [pollId: bigint, isActive: boolean];
  export interface OutputObject {
    pollId: bigint;
    isActive: boolean;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace VoteCastEvent {
  export type InputTuple = [
    pollId: BigNumberish,
    voter: AddressLike,
    optionIndex: BigNumberish
  ];
  export type OutputTuple = [
    pollId: bigint,
    voter: string,
    optionIndex: bigint
  ];
  export interface OutputObject {
    pollId: bigint;
    voter: string;
    optionIndex: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface SimpleVoting extends BaseContract {
  connect(runner?: ContractRunner | null): SimpleVoting;
  waitForDeployment(): Promise<this>;

  interface: SimpleVotingInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  createPoll: TypedContractMethod<
    [_question: string, _options: string[]],
    [bigint],
    "nonpayable"
  >;

  creatorPolls: TypedContractMethod<
    [arg0: AddressLike, arg1: BigNumberish],
    [bigint],
    "view"
  >;

  getCreatorPolls: TypedContractMethod<
    [_creator: AddressLike],
    [bigint[]],
    "view"
  >;

  getPoll: TypedContractMethod<
    [_pollId: BigNumberish],
    [
      [string, string[], bigint[], boolean, string, bigint] & {
        question: string;
        options: string[];
        votes: bigint[];
        isActive: boolean;
        creator: string;
        createdAt: bigint;
      }
    ],
    "view"
  >;

  getPollCount: TypedContractMethod<[], [bigint], "view">;

  getUserVote: TypedContractMethod<
    [_pollId: BigNumberish, _user: AddressLike],
    [bigint],
    "view"
  >;

  hasUserVoted: TypedContractMethod<
    [_pollId: BigNumberish, _user: AddressLike],
    [boolean],
    "view"
  >;

  pollCount: TypedContractMethod<[], [bigint], "view">;

  polls: TypedContractMethod<
    [arg0: BigNumberish],
    [
      [string, boolean, string, bigint] & {
        question: string;
        isActive: boolean;
        creator: string;
        createdAt: bigint;
      }
    ],
    "view"
  >;

  togglePollStatus: TypedContractMethod<
    [_pollId: BigNumberish],
    [void],
    "nonpayable"
  >;

  vote: TypedContractMethod<
    [_pollId: BigNumberish, _optionIndex: BigNumberish],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "createPoll"
  ): TypedContractMethod<
    [_question: string, _options: string[]],
    [bigint],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "creatorPolls"
  ): TypedContractMethod<
    [arg0: AddressLike, arg1: BigNumberish],
    [bigint],
    "view"
  >;
  getFunction(
    nameOrSignature: "getCreatorPolls"
  ): TypedContractMethod<[_creator: AddressLike], [bigint[]], "view">;
  getFunction(
    nameOrSignature: "getPoll"
  ): TypedContractMethod<
    [_pollId: BigNumberish],
    [
      [string, string[], bigint[], boolean, string, bigint] & {
        question: string;
        options: string[];
        votes: bigint[];
        isActive: boolean;
        creator: string;
        createdAt: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "getPollCount"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "getUserVote"
  ): TypedContractMethod<
    [_pollId: BigNumberish, _user: AddressLike],
    [bigint],
    "view"
  >;
  getFunction(
    nameOrSignature: "hasUserVoted"
  ): TypedContractMethod<
    [_pollId: BigNumberish, _user: AddressLike],
    [boolean],
    "view"
  >;
  getFunction(
    nameOrSignature: "pollCount"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "polls"
  ): TypedContractMethod<
    [arg0: BigNumberish],
    [
      [string, boolean, string, bigint] & {
        question: string;
        isActive: boolean;
        creator: string;
        createdAt: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "togglePollStatus"
  ): TypedContractMethod<[_pollId: BigNumberish], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "vote"
  ): TypedContractMethod<
    [_pollId: BigNumberish, _optionIndex: BigNumberish],
    [void],
    "nonpayable"
  >;

  getEvent(
    key: "PollCreated"
  ): TypedContractEvent<
    PollCreatedEvent.InputTuple,
    PollCreatedEvent.OutputTuple,
    PollCreatedEvent.OutputObject
  >;
  getEvent(
    key: "PollStatusChanged"
  ): TypedContractEvent<
    PollStatusChangedEvent.InputTuple,
    PollStatusChangedEvent.OutputTuple,
    PollStatusChangedEvent.OutputObject
  >;
  getEvent(
    key: "VoteCast"
  ): TypedContractEvent<
    VoteCastEvent.InputTuple,
    VoteCastEvent.OutputTuple,
    VoteCastEvent.OutputObject
  >;

  filters: {
    "PollCreated(uint256,address,string,uint256)": TypedContractEvent<
      PollCreatedEvent.InputTuple,
      PollCreatedEvent.OutputTuple,
      PollCreatedEvent.OutputObject
    >;
    PollCreated: TypedContractEvent<
      PollCreatedEvent.InputTuple,
      PollCreatedEvent.OutputTuple,
      PollCreatedEvent.OutputObject
    >;

    "PollStatusChanged(uint256,bool)": TypedContractEvent<
      PollStatusChangedEvent.InputTuple,
      PollStatusChangedEvent.OutputTuple,
      PollStatusChangedEvent.OutputObject
    >;
    PollStatusChanged: TypedContractEvent<
      PollStatusChangedEvent.InputTuple,
      PollStatusChangedEvent.OutputTuple,
      PollStatusChangedEvent.OutputObject
    >;

    "VoteCast(uint256,address,uint256)": TypedContractEvent<
      VoteCastEvent.InputTuple,
      VoteCastEvent.OutputTuple,
      VoteCastEvent.OutputObject
    >;
    VoteCast: TypedContractEvent<
      VoteCastEvent.InputTuple,
      VoteCastEvent.OutputTuple,
      VoteCastEvent.OutputObject
    >;
  };
}
