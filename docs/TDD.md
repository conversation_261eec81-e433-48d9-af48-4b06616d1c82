# 投票 DApp 智能合约技术文档

## 项目概述

这是一个用于学习演示的投票 DApp MVP 项目，目标是在 1-2 天内完成一个功能完整但简化的去中心化投票系统。

**技术栈：**

- 智能合约：Solidity + Hardhat
- 区块链：Base Sepolia 测试网
- 前端：Next.js 15 + TypeScript + Wagmi + Viem + RainbowKit

## 1. 合约功能清单

### 1.1 核心功能（必须实现）

- ✅ **创建投票**：用户可以创建新的投票主题
- ✅ **投票**：用户可以对投票选项进行投票
- ✅ **查看投票结果**：实时查看投票统计
- ✅ **防重复投票**：每个地址只能投票一次
- ✅ **投票状态管理**：投票的激活/停用状态

### 1.2 不包含的功能（避免功能蔓延）

- ❌ 投票权重系统
- ❌ 投票委托功能
- ❌ 复杂的治理机制
- ❌ 投票时间限制
- ❌ 投票修改功能
- ❌ 管理员权限系统
- ❌ 投票费用机制

## 2. 数据结构定义

### 2.1 Poll 结构体

```solidity
struct Poll {
    string question;                    // 投票问题
    string[] options;                   // 投票选项数组
    uint256[] votes;                    // 每个选项的票数
    mapping(address => bool) hasVoted;  // 记录用户是否已投票
    mapping(address => uint256) voterChoice; // 记录用户的投票选择
    bool isActive;                      // 投票是否激活
    address creator;                    // 投票创建者
    uint256 createdAt;                  // 创建时间
}
```

### 2.2 存储变量

```solidity
contract SimpleVoting {
    Poll[] public polls;                // 所有投票的数组
    uint256 public pollCount;           // 投票总数（可选，polls.length也可以）

    // 可选：创建者到投票ID的映射
    mapping(address => uint256[]) public creatorPolls;
}
```

### 2.3 与前端 TypeScript 类型对应

```typescript
// TypeScript接口定义
interface PollData {
  id: bigint;
  question: string;
  options: string[];
  votes: bigint[];
  isActive: boolean;
  creator: `0x${string}`;
  createdAt: bigint;
  totalVotes: bigint;
}

interface UserVoteStatus {
  hasVoted: boolean;
  choice?: number;
}
```

## 3. 函数接口规范

### 3.1 写入函数（需要 Gas 费用）

#### createPoll

```solidity
function createPoll(
    string memory _question,
    string[] memory _options
) external returns (uint256 pollId)
```

**参数验证：**

- `_question`不能为空
- `_options`长度必须 >= 2 且 <= 10
- 每个选项不能为空

**Wagmi Hook 对接：**

```typescript
const { writeContract } = useWriteContract();

const createPoll = (question: string, options: string[]) => {
  writeContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: 'createPoll',
    args: [question, options],
  });
};
```

#### vote

```solidity
function vote(
    uint256 _pollId,
    uint256 _optionIndex
) external
```

**参数验证：**

- `_pollId`必须存在
- `_optionIndex`必须在有效范围内
- 用户未曾投票
- 投票必须处于激活状态

**Wagmi Hook 对接：**

```typescript
const { writeContract } = useWriteContract();

const vote = (pollId: bigint, optionIndex: bigint) => {
  writeContract({
    address: CONTRACT_ADDRESS,
    abi: CONTRACT_ABI,
    functionName: 'vote',
    args: [pollId, optionIndex],
  });
};
```

### 3.2 只读函数（无 Gas 费用）

#### getPoll

```solidity
function getPoll(uint256 _pollId)
    external
    view
    returns (
        string memory question,
        string[] memory options,
        uint256[] memory votes,
        bool isActive,
        address creator,
        uint256 createdAt
    )
```

**Wagmi Hook 对接：**

```typescript
const { data: poll } = useReadContract({
  address: CONTRACT_ADDRESS,
  abi: CONTRACT_ABI,
  functionName: 'getPoll',
  args: [pollId],
});
```

#### getPollCount

```solidity
function getPollCount() external view returns (uint256)
```

#### hasUserVoted

```solidity
function hasUserVoted(
    uint256 _pollId,
    address _user
) external view returns (bool)
```

#### getUserVote

```solidity
function getUserVote(
    uint256 _pollId,
    address _user
) external view returns (uint256)
```

## 4. 事件定义

### 4.1 事件列表

```solidity
event PollCreated(
    uint256 indexed pollId,
    address indexed creator,
    string question,
    uint256 optionCount
);

event VoteCast(
    uint256 indexed pollId,
    address indexed voter,
    uint256 optionIndex
);

event PollStatusChanged(
    uint256 indexed pollId,
    bool isActive
);
```

### 4.2 前端事件监听

```typescript
// 监听投票创建事件
const { data: logs } = useWatchContractEvent({
  address: CONTRACT_ADDRESS,
  abi: CONTRACT_ABI,
  eventName: 'PollCreated',
  onLogs(logs) {
    console.log('New poll created:', logs);
    // 刷新投票列表
  },
});

// 监听投票事件
const { data: voteLogs } = useWatchContractEvent({
  address: CONTRACT_ADDRESS,
  abi: CONTRACT_ABI,
  eventName: 'VoteCast',
  args: {
    pollId: currentPollId, // 监听特定投票
  },
  onLogs(logs) {
    console.log('New vote cast:', logs);
    // 刷新投票结果
  },
});
```

## 5. 基础安全措施

### 5.1 防重复投票

```solidity
modifier hasNotVoted(uint256 _pollId) {
    require(!polls[_pollId].hasVoted[msg.sender], "Already voted");
    _;
}

function vote(uint256 _pollId, uint256 _optionIndex)
    external
    hasNotVoted(_pollId)
{
    // 投票逻辑
    polls[_pollId].hasVoted[msg.sender] = true;
    polls[_pollId].voterChoice[msg.sender] = _optionIndex;
    polls[_pollId].votes[_optionIndex]++;
}
```

### 5.2 基本权限检查

```solidity
modifier pollExists(uint256 _pollId) {
    require(_pollId < polls.length, "Poll does not exist");
    _;
}

modifier pollActive(uint256 _pollId) {
    require(polls[_pollId].isActive, "Poll is not active");
    _;
}

modifier validOption(uint256 _pollId, uint256 _optionIndex) {
    require(_optionIndex < polls[_pollId].options.length, "Invalid option");
    _;
}
```

### 5.3 输入验证

```solidity
modifier validPollData(string memory _question, string[] memory _options) {
    require(bytes(_question).length > 0, "Question cannot be empty");
    require(_options.length >= 2, "At least 2 options required");
    require(_options.length <= 10, "Too many options");

    for (uint i = 0; i < _options.length; i++) {
        require(bytes(_options[i]).length > 0, "Option cannot be empty");
    }
    _;
}
```

## 6. 部署配置

### 6.1 Hardhat 配置

```typescript
// hardhat.config.ts
import { HardhatUserConfig } from 'hardhat/config';
import '@nomicfoundation/hardhat-toolbox';
import '@nomicfoundation/hardhat-verify';

const config: HardhatUserConfig = {
  solidity: {
    version: '0.8.19',
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    'base-sepolia': {
      url: 'https://sepolia.base.org',
      accounts: [process.env.PRIVATE_KEY!],
      chainId: 84532,
    },
  },
  etherscan: {
    apiKey: {
      'base-sepolia': process.env.BASESCAN_API_KEY!,
    },
    customChains: [
      {
        network: 'base-sepolia',
        chainId: 84532,
        urls: {
          apiURL: 'https://api-sepolia.basescan.org/api',
          browserURL: 'https://sepolia.basescan.org',
        },
      },
    ],
  },
};

export default config;
```

### 6.2 部署脚本

```typescript
// scripts/deploy.ts
import { ethers } from 'hardhat';

async function main() {
  console.log('Deploying SimpleVoting contract...');

  const SimpleVoting = await ethers.getContractFactory('SimpleVoting');
  const simpleVoting = await SimpleVoting.deploy();

  await simpleVoting.waitForDeployment();
  const address = await simpleVoting.getAddress();

  console.log(`SimpleVoting deployed to: ${address}`);

  // 保存合约地址到文件
  const fs = require('fs');
  const contractAddresses = {
    SimpleVoting: address,
    network: 'base-sepolia',
    chainId: 84532,
  };

  fs.writeFileSync(
    'contract-addresses.json',
    JSON.stringify(contractAddresses, null, 2)
  );
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
```

### 6.3 环境变量设置

```bash
# .env
PRIVATE_KEY=your_private_key_here
BASESCAN_API_KEY=your_basescan_api_key
NEXT_PUBLIC_CONTRACT_ADDRESS=deployed_contract_address
NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your_walletconnect_project_id
```

## 7. 测试要点

### 7.1 必须测试的核心功能

```typescript
// test/SimpleVoting.test.ts
import { expect } from 'chai';
import { ethers } from 'hardhat';

describe('SimpleVoting', function () {
  let simpleVoting: any;
  let owner: any;
  let addr1: any;
  let addr2: any;

  beforeEach(async function () {
    [owner, addr1, addr2] = await ethers.getSigners();
    const SimpleVoting = await ethers.getContractFactory('SimpleVoting');
    simpleVoting = await SimpleVoting.deploy();
  });

  describe('Poll Creation', function () {
    it('Should create a poll with valid data', async function () {
      const question = "What's your favorite color?";
      const options = ['Red', 'Blue', 'Green'];

      await expect(simpleVoting.createPoll(question, options))
        .to.emit(simpleVoting, 'PollCreated')
        .withArgs(0, owner.address, question, options.length);
    });

    it('Should reject empty question', async function () {
      await expect(
        simpleVoting.createPoll('', ['Option1', 'Option2'])
      ).to.be.revertedWith('Question cannot be empty');
    });

    it('Should reject insufficient options', async function () {
      await expect(
        simpleVoting.createPoll('Question?', ['Option1'])
      ).to.be.revertedWith('At least 2 options required');
    });
  });

  describe('Voting', function () {
    beforeEach(async function () {
      await simpleVoting.createPoll('Test Poll', ['Option1', 'Option2']);
    });

    it('Should allow valid vote', async function () {
      await expect(simpleVoting.connect(addr1).vote(0, 0))
        .to.emit(simpleVoting, 'VoteCast')
        .withArgs(0, addr1.address, 0);
    });

    it('Should prevent double voting', async function () {
      await simpleVoting.connect(addr1).vote(0, 0);
      await expect(simpleVoting.connect(addr1).vote(0, 1)).to.be.revertedWith(
        'Already voted'
      );
    });

    it('Should reject invalid option', async function () {
      await expect(simpleVoting.connect(addr1).vote(0, 5)).to.be.revertedWith(
        'Invalid option'
      );
    });
  });

  describe('Data Retrieval', function () {
    it('Should return correct poll data', async function () {
      const question = 'Test Question';
      const options = ['A', 'B', 'C'];

      await simpleVoting.createPoll(question, options);
      const poll = await simpleVoting.getPoll(0);

      expect(poll.question).to.equal(question);
      expect(poll.options).to.deep.equal(options);
      expect(poll.isActive).to.be.true;
    });
  });
});
```

### 7.2 测试运行命令

```bash
# 运行测试
pnpm hardhat test

# 运行测试并查看覆盖率
pnpm hardhat coverage

# 运行特定测试文件
pnpm hardhat test test/SimpleVoting.test.ts
```

## 8. 开发检查清单

### 8.1 Day 1: 智能合约开发（4-6 小时）

#### 上午（2-3 小时）

- [ ] **项目初始化**（30 分钟）

  - [ ] 创建 Hardhat 项目
  - [ ] 安装必要依赖
  - [ ] 配置 hardhat.config.ts

- [ ] **合约编写**（1.5-2 小时）

  - [ ] 定义 Poll 结构体
  - [ ] 实现 createPoll 函数
  - [ ] 实现 vote 函数
  - [ ] 实现查询函数
  - [ ] 添加事件定义

- [ ] **基础验证**（30 分钟）
  - [ ] 合约编译成功
  - [ ] 无明显语法错误

#### 下午（2-3 小时）

- [ ] **测试编写**（1-1.5 小时）

  - [ ] 创建测试文件
  - [ ] 测试投票创建功能
  - [ ] 测试投票功能
  - [ ] 测试防重复投票
  - [ ] 测试数据查询

- [ ] **本地部署测试**（30 分钟）

  - [ ] 本地网络部署
  - [ ] 基本功能验证

- [ ] **测试网部署**（1 小时）
  - [ ] 配置 Base Sepolia 网络
  - [ ] 部署到测试网
  - [ ] 合约验证
  - [ ] 记录合约地址

**Day 1 验收标准：**

- ✅ 合约成功部署到 Base Sepolia
- ✅ 所有测试用例通过
- ✅ 合约在 BaseScan 上验证成功
- ✅ 基本功能可通过 Etherscan 调用验证

### 8.2 Day 2: 前端集成（4-6 小时）

#### 上午（2-3 小时）

- [ ] **Wagmi 配置**（1 小时）

  - [ ] 配置 wagmi.ts
  - [ ] 设置 RainbowKit
  - [ ] 配置合约 ABI 和地址

- [ ] **自定义 Hooks**（1-2 小时）
  - [ ] 创建 useVotingContract.ts
  - [ ] 实现读取 hooks
  - [ ] 实现写入 hooks

#### 下午（2-3 小时）

- [ ] **组件开发**（1.5-2 小时）

  - [ ] ConnectWallet 组件
  - [ ] CreatePollForm 组件
  - [ ] VoteForm 组件
  - [ ] PollList 组件

- [ ] **页面集成**（30 分钟-1 小时）
  - [ ] 创建投票页面
  - [ ] 投票列表页面
  - [ ] 投票详情页面

**Day 2 验收标准：**

- ✅ 钱包连接正常
- ✅ 可以创建投票
- ✅ 可以进行投票
- ✅ 可以查看投票结果
- ✅ 基本错误处理正常

### 8.3 最终验收标准

**功能验收：**

- [ ] 用户可以连接 MetaMask 钱包
- [ ] 用户可以创建新投票（至少 2 个选项）
- [ ] 用户可以对投票进行投票
- [ ] 用户不能重复投票
- [ ] 可以实时查看投票结果
- [ ] 基本的错误提示正常显示

**技术验收：**

- [ ] 合约部署在 Base Sepolia 测试网
- [ ] 前端部署在 Vercel
- [ ] 所有核心功能正常工作
- [ ] 响应式设计基本可用
- [ ] 无明显的 UI/UX 问题

## 9. 常见问题和解决方案

### 9.1 合约开发问题

**问题：编译错误**

```bash
# 解决方案：检查Solidity版本
pragma solidity ^0.8.19; // 确保版本一致
```

**问题：Gas 估算失败**

```solidity
// 解决方案：添加适当的require检查
require(_pollId < polls.length, "Poll does not exist");
```

### 9.2 前端集成问题

**问题：合约调用失败**

```typescript
// 解决方案：检查网络和合约地址
const { chain } = useAccount();
if (chain?.id !== 84532) {
  // 提示用户切换网络
}
```

**问题：事件监听不工作**

```typescript
// 解决方案：确保事件名称和参数正确
const { data: logs } = useWatchContractEvent({
  address: CONTRACT_ADDRESS,
  abi: CONTRACT_ABI,
  eventName: 'PollCreated', // 确保名称正确
});
```

## 10. 部署后验证步骤

1. **合约验证**

   - [ ] 在 BaseScan 上查看合约
   - [ ] 验证合约源码
   - [ ] 测试合约函数调用

2. **前端验证**

   - [ ] Vercel 部署成功
   - [ ] 钱包连接正常
   - [ ] 合约交互正常

3. **端到端测试**
   - [ ] 创建测试投票
   - [ ] 多个账户投票测试
   - [ ] 结果显示验证

这份文档提供了完整的 1-2 天开发指南，重点关注 MVP 的快速实现而非完美优化。按照这个清单执行，可以确保在时间限制内完成一个功能完整的学习演示项目。
