'use client';

import { usePollCount, usePoll, transformPollData } from '@/hooks/useVotingContract';
import { VoteCard } from './VoteCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export function PollList() {
  const { data: pollCount, isLoading: isLoadingCount } = usePollCount();

  if (isLoadingCount) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-card border-border shadow-md animate-pulse">
            <CardHeader>
              <div className="h-6 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-10 bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!pollCount || pollCount === 0n) {
    return (
      <Card className="bg-card border-border shadow-md">
        <CardHeader>
          <CardTitle className="text-card-foreground">No Polls Yet</CardTitle>
          <CardDescription className="text-muted-foreground">
            Be the first to create a poll!
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const pollIds = Array.from({ length: Number(pollCount) }, (_, i) => BigInt(i));

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground">
          Active Polls ({Number(pollCount)})
        </h2>
        <p className="text-muted-foreground">
          Vote on the polls below or create your own
        </p>
      </div>
      
      <div className="space-y-4">
        {pollIds.reverse().map((pollId) => (
          <PollItem key={pollId.toString()} pollId={pollId} />
        ))}
      </div>
    </div>
  );
}

function PollItem({ pollId }: { pollId: bigint }) {
  const { data: pollData, isLoading, refetch } = usePoll(pollId);

  if (isLoading) {
    return (
      <Card className="bg-card border-border shadow-md animate-pulse">
        <CardHeader>
          <div className="h-6 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-10 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!pollData) {
    return null;
  }

  const poll = transformPollData(pollData, pollId);

  return (
    <VoteCard 
      poll={poll} 
      onVoteSuccess={() => {
        refetch();
      }}
    />
  );
}
