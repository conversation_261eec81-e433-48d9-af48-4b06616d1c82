"use client";

import { useState } from "react";
import { useAccount } from "wagmi";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  useVote,
  voteArgs,
  useUserVoteStatus,
} from "@/hooks/useVotingContract";
import { PollData } from "@/lib/types";

interface VoteCardProps {
  poll: PollData;
  onVoteSuccess?: () => void;
}

export function VoteCard({ poll, onVoteSuccess }: VoteCardProps) {
  const { address, isConnected } = useAccount();
  const { writeContract, isPending, error } = useVote();
  const userVoteStatus = useUserVoteStatus(poll.id, address);

  const [selectedOption, setSelectedOption] = useState<number | null>(null);

  const handleVote = async () => {
    if (!isConnected || !address || selectedOption === null) {
      alert("Please connect your wallet and select an option");
      return;
    }

    if (userVoteStatus.hasVoted) {
      alert("You have already voted on this poll");
      return;
    }

    try {
      await writeContract(voteArgs(poll.id, BigInt(selectedOption)));
      onVoteSuccess?.();
    } catch (err) {
      console.error("Failed to vote:", err);
    }
  };

  const getVotePercentage = (optionIndex: number): number => {
    if (poll.totalVotes === 0n) return 0;
    return Number((poll.votes[optionIndex] * 100n) / poll.totalVotes);
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatTimestamp = (timestamp: bigint) => {
    return new Date(Number(timestamp) * 1000).toLocaleDateString();
  };

  return (
    <Card className="bg-card border-border shadow-md">
      <CardHeader>
        <CardTitle className="text-card-foreground">{poll.question}</CardTitle>
        <CardDescription className="text-muted-foreground">
          Created by {formatAddress(poll.creator)} on{" "}
          {formatTimestamp(poll.createdAt)}
          {!poll.isActive && (
            <span className="ml-2 text-destructive font-medium">
              (Inactive)
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Voting Options */}
        <div className="space-y-3">
          {poll.options.map((option, index) => {
            const voteCount = Number(poll.votes[index]);
            const percentage = getVotePercentage(index);
            const isUserChoice =
              userVoteStatus.hasVoted && userVoteStatus.choice === index;

            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-foreground flex items-center gap-2">
                    {!userVoteStatus.hasVoted && poll.isActive && (
                      <input
                        type="radio"
                        name={`poll-${poll.id}`}
                        value={index}
                        checked={selectedOption === index}
                        onChange={() => setSelectedOption(index)}
                        className="accent-primary"
                      />
                    )}
                    <span
                      className={
                        isUserChoice ? "font-semibold text-primary" : ""
                      }
                    >
                      {option}
                      {isUserChoice && " ✓"}
                    </span>
                  </Label>
                  <span className="text-sm text-muted-foreground">
                    {voteCount} votes ({percentage.toFixed(1)}%)
                  </span>
                </div>

                {/* Progress bar */}
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      isUserChoice ? "bg-primary" : "bg-secondary"
                    }`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Vote Summary */}
        <div className="pt-4 border-t border-border">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Total Votes: {Number(poll.totalVotes)}</span>
            <span>Poll ID: #{Number(poll.id)}</span>
          </div>
        </div>

        {/* Vote Button */}
        {!userVoteStatus.hasVoted && poll.isActive && (
          <div className="pt-2">
            {error && (
              <div className="text-destructive text-sm mb-2">
                Error: {error.message}
              </div>
            )}

            <Button
              onClick={handleVote}
              disabled={!isConnected || selectedOption === null || isPending}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
            >
              {isPending ? "Voting..." : "Cast Vote"}
            </Button>
          </div>
        )}

        {userVoteStatus.hasVoted && (
          <div className="pt-2">
            <div className="text-center text-sm text-muted-foreground bg-muted p-2 rounded-md">
              You have already voted on this poll
            </div>
          </div>
        )}

        {!poll.isActive && (
          <div className="pt-2">
            <div className="text-center text-sm text-destructive bg-destructive/10 p-2 rounded-md">
              This poll is no longer active
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
