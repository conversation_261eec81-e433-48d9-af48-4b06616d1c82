"use client";

import { useState } from "react";
import { useAccount } from "wagmi";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCreatePoll, createPollArgs } from "@/hooks/useVotingContract";
import { CreatePollForm as CreatePollFormType } from "@/lib/types";

export function CreatePollForm({ onSuccess }: { onSuccess?: () => void }) {
  const { address, isConnected } = useAccount();
  const { writeContract, isPending, error } = useCreatePoll();

  const [formData, setFormData] = useState<CreatePollFormType>({
    question: "",
    options: ["", ""],
  });

  const addOption = () => {
    if (formData.options.length < 10) {
      setFormData(prev => ({
        ...prev,
        options: [...prev.options, ""],
      }));
    }
  };

  const removeOption = (index: number) => {
    if (formData.options.length > 2) {
      setFormData(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index),
      }));
    }
  };

  const updateOption = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) => (i === index ? value : option)),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isConnected || !address) {
      alert("Please connect your wallet first");
      return;
    }

    // Validate form
    if (!formData.question.trim()) {
      alert("Please enter a question");
      return;
    }

    const validOptions = formData.options.filter(option => option.trim());
    if (validOptions.length < 2) {
      alert("Please provide at least 2 options");
      return;
    }

    try {
      await writeContract(createPollArgs(formData.question, validOptions));

      // Reset form on success
      setFormData({
        question: "",
        options: ["", ""],
      });

      onSuccess?.();
    } catch (err) {
      console.error("Failed to create poll:", err);
    }
  };

  return (
    <Card className="bg-card border-border shadow-md">
      <CardHeader>
        <CardTitle className="text-card-foreground">Create New Poll</CardTitle>
        <CardDescription className="text-muted-foreground">
          Create a new voting poll for the community
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="question" className="text-foreground">
              Poll Question
            </Label>
            <Textarea
              id="question"
              placeholder="What would you like to ask?"
              value={formData.question}
              onChange={e =>
                setFormData(prev => ({ ...prev, question: e.target.value }))
              }
              className="bg-background border-border text-foreground placeholder:text-muted-foreground"
              required
            />
          </div>

          <div className="space-y-2">
            <Label className="text-foreground">Options</Label>
            {formData.options.map((option, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder={`Option ${index + 1}`}
                  value={option}
                  onChange={e => updateOption(index, e.target.value)}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
                {formData.options.length > 2 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => removeOption(index)}
                    className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  >
                    Remove
                  </Button>
                )}
              </div>
            ))}

            {formData.options.length < 10 && (
              <Button
                type="button"
                variant="outline"
                onClick={addOption}
                className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              >
                Add Option
              </Button>
            )}
          </div>

          {error && (
            <div className="text-destructive text-sm">
              Error: {error.message}
            </div>
          )}

          <Button
            type="submit"
            disabled={!isConnected || isPending}
            className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
          >
            {isPending ? "Creating Poll..." : "Create Poll"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
