{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noEmit": false, "declaration": true, "outDir": "./dist", "typeRoots": ["./node_modules/@types"]}, "include": ["hardhat.config.ts", "scripts/**/*", "test/**/*", "contracts/**/*"], "exclude": ["node_modules", "dist", "src/**/*"]}