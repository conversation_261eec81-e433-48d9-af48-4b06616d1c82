const { expect } = require('chai');
const { ethers } = require('hardhat');

describe('SimpleVoting', function () {
  let simpleVoting;
  let owner;
  let addr1;
  let addr2;

  beforeEach(async function () {
    [owner, addr1, addr2] = await ethers.getSigners();
    const SimpleVoting = await ethers.getContractFactory('SimpleVoting');
    simpleVoting = await SimpleVoting.deploy();
  });

  describe('Poll Creation', function () {
    it('Should create a poll with valid data', async function () {
      const question = "What's your favorite color?";
      const options = ['Red', 'Blue', 'Green'];

      await expect(simpleVoting.createPoll(question, options))
        .to.emit(simpleVoting, 'PollCreated')
        .withArgs(0, owner.address, question, options.length);

      const pollCount = await simpleVoting.getPollCount();
      expect(pollCount).to.equal(1);
    });

    it('Should reject empty question', async function () {
      await expect(
        simpleVoting.createPoll('', ['Option1', 'Option2'])
      ).to.be.revertedWith('Question cannot be empty');
    });

    it('Should reject insufficient options', async function () {
      await expect(
        simpleVoting.createPoll('Question?', ['Option1'])
      ).to.be.revertedWith('At least 2 options required');
    });

    it('Should reject too many options', async function () {
      const manyOptions = Array.from(
        { length: 11 },
        (_, i) => `Option${i + 1}`
      );
      await expect(
        simpleVoting.createPoll('Question?', manyOptions)
      ).to.be.revertedWith('Too many options');
    });

    it('Should reject empty options', async function () {
      await expect(
        simpleVoting.createPoll('Question?', ['Option1', ''])
      ).to.be.revertedWith('Option cannot be empty');
    });
  });

  describe('Voting', function () {
    beforeEach(async function () {
      await simpleVoting.createPoll('Test Poll', ['Option1', 'Option2']);
    });

    it('Should allow valid vote', async function () {
      await expect(simpleVoting.connect(addr1).vote(0, 0))
        .to.emit(simpleVoting, 'VoteCast')
        .withArgs(0, addr1.address, 0);

      const hasVoted = await simpleVoting.hasUserVoted(0, addr1.address);
      expect(hasVoted).to.be.true;

      const userVote = await simpleVoting.getUserVote(0, addr1.address);
      expect(userVote).to.equal(0);
    });

    it('Should prevent double voting', async function () {
      await simpleVoting.connect(addr1).vote(0, 0);
      await expect(simpleVoting.connect(addr1).vote(0, 1)).to.be.revertedWith(
        'Already voted'
      );
    });

    it('Should reject invalid option', async function () {
      await expect(simpleVoting.connect(addr1).vote(0, 5)).to.be.revertedWith(
        'Invalid option'
      );
    });

    it('Should reject voting on non-existent poll', async function () {
      await expect(simpleVoting.connect(addr1).vote(999, 0)).to.be.revertedWith(
        'Poll does not exist'
      );
    });

    it('Should update vote counts correctly', async function () {
      await simpleVoting.connect(addr1).vote(0, 0);
      await simpleVoting.connect(addr2).vote(0, 1);

      const poll = await simpleVoting.getPoll(0);
      expect(poll.votes[0]).to.equal(1);
      expect(poll.votes[1]).to.equal(1);
    });
  });

  describe('Data Retrieval', function () {
    it('Should return correct poll data', async function () {
      const question = 'Test Question';
      const options = ['A', 'B', 'C'];

      await simpleVoting.createPoll(question, options);
      const poll = await simpleVoting.getPoll(0);

      expect(poll.question).to.equal(question);
      expect(poll.options).to.deep.equal(options);
      expect(poll.isActive).to.be.true;
      expect(poll.creator).to.equal(owner.address);
      expect(poll.votes.length).to.equal(options.length);
    });

    it('Should return correct poll count', async function () {
      expect(await simpleVoting.getPollCount()).to.equal(0);

      await simpleVoting.createPoll('Poll 1', ['A', 'B']);
      expect(await simpleVoting.getPollCount()).to.equal(1);

      await simpleVoting.createPoll('Poll 2', ['X', 'Y']);
      expect(await simpleVoting.getPollCount()).to.equal(2);
    });

    it('Should track creator polls correctly', async function () {
      await simpleVoting.connect(owner).createPoll('Poll 1', ['A', 'B']);
      await simpleVoting.connect(addr1).createPoll('Poll 2', ['X', 'Y']);
      await simpleVoting.connect(owner).createPoll('Poll 3', ['M', 'N']);

      const ownerPolls = await simpleVoting.getCreatorPolls(owner.address);
      const addr1Polls = await simpleVoting.getCreatorPolls(addr1.address);

      expect(ownerPolls.length).to.equal(2);
      expect(ownerPolls[0]).to.equal(0);
      expect(ownerPolls[1]).to.equal(2);

      expect(addr1Polls.length).to.equal(1);
      expect(addr1Polls[0]).to.equal(1);
    });
  });

  describe('Poll Status Management', function () {
    beforeEach(async function () {
      await simpleVoting.createPoll('Test Poll', ['Option1', 'Option2']);
    });

    it('Should allow creator to toggle poll status', async function () {
      await expect(simpleVoting.togglePollStatus(0))
        .to.emit(simpleVoting, 'PollStatusChanged')
        .withArgs(0, false);

      const poll = await simpleVoting.getPoll(0);
      expect(poll.isActive).to.be.false;
    });

    it('Should prevent non-creator from toggling status', async function () {
      await expect(
        simpleVoting.connect(addr1).togglePollStatus(0)
      ).to.be.revertedWith('Only creator can toggle status');
    });

    it('Should prevent voting on inactive poll', async function () {
      await simpleVoting.togglePollStatus(0);
      await expect(simpleVoting.connect(addr1).vote(0, 0)).to.be.revertedWith(
        'Poll is not active'
      );
    });
  });

  describe('Edge Cases', function () {
    it('Should handle getUserVote for non-voter', async function () {
      await simpleVoting.createPoll('Test Poll', ['Option1', 'Option2']);

      await expect(
        simpleVoting.getUserVote(0, addr1.address)
      ).to.be.revertedWith('User has not voted');
    });

    it('Should handle hasUserVoted for non-existent poll', async function () {
      await expect(
        simpleVoting.hasUserVoted(999, addr1.address)
      ).to.be.revertedWith('Poll does not exist');
    });
  });
});
